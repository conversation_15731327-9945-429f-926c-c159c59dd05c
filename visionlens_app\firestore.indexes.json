{"indexes": [{"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "price", "order": "ASCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "brand", "order": "ASCENDING"}, {"fieldPath": "price", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "reviews", "queryScope": "COLLECTION", "fields": [{"fieldPath": "productId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": [{"collectionGroup": "products", "fieldPath": "searchTerms", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}, {"collectionGroup": "products", "fieldPath": "tags", "indexes": [{"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}]}