import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'app_properties.dart';
import 'screens/splash_page.dart';
import 'services/firebase_mock_service.dart';
import 'services/firebase_platform_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // تهيئة Firebase Core أولاً
    if (kDebugMode) {
      print('🔥 بدء تهيئة Firebase Core...');
    }

    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    if (kDebugMode) {
      print('✅ تم تهيئة Firebase Core بنجاح');
      print(
        '🔗 رابط المشروع: https://console.firebase.google.com/project/visionlens-app-5ab70/firestore',
      );
    }

    // استخدم Firebase Platform Service (يختار الخدمة المناسبة حسب المنصة)
    final platformSuccess = await FirebasePlatformService.initialize();
    if (platformSuccess) {
      if (kDebugMode) {
        if (kIsWeb) {
          print('✅ Firebase Platform Service تم تهيئته بنجاح للويب');
          print('🌐 يستخدم Firebase JavaScript SDK');
        } else {
          print('✅ Firebase Platform Service تم تهيئته بنجاح للموبايل');
          print('📱 يستخدم Firebase Mobile SDK');
        }
      }

      // Firebase Platform Service جاهز للاستخدام
      if (kDebugMode) {
        print('🔗 Firebase Platform Service جاهز لجلب البيانات الحقيقية');
      }
    } else {
      throw Exception('فشل في تهيئة Firebase Platform Service');
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ خطأ في تهيئة Firebase: $e');
      print('🔄 التبديل للخدمة المحاكية...');
    }

    // في حالة فشل Firebase، استخدم المحاكي
    try {
      await FirebaseMockService.initialize();
      if (kDebugMode) {
        print('✅ Firebase Mock Service المحسن تم تهيئته بنجاح');
        print('🔥 يدعم Google Sign-In الحقيقي!');
      }
    } catch (e2) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة Firebase Mock Service: $e2');
      }
    }
  }

  runApp(const VisionLensApp());
}

class VisionLensApp extends StatelessWidget {
  const VisionLensApp({super.key});

  @override
  Widget build(BuildContext context) {
    // تعيين اتجاه النص للعربية
    return MaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,

      // دعم اللغة العربية
      locale: const Locale('ar', 'SA'),

      // تعيين اتجاه النص من اليمين لليسار
      builder: (context, child) {
        return Directionality(textDirection: TextDirection.rtl, child: child!);
      },

      home: const SplashPage(),
    );
  }
}
