# 🔑 إعداد Client ID للتطبيق

## ✅ تم إنشاء Client ID في Google Cloud Console؟

إذا اتبعت التعليمات وحصلت على Client ID، قم بالخطوات التالية:

### 1. نسخ Client ID

Client ID يبدو مثل:
```
123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com
```

### 2. تحديث الكود

#### أ. في ملف `lib/services/auth_service_real_google.dart` السطر 17:

**ابحث عن**:
```dart
clientId: 'ضع_CLIENT_ID_هنا.apps.googleusercontent.com',
```

**استبدله بـ**:
```dart
clientId: 'CLIENT_ID_الخاص_بك.apps.googleusercontent.com',
```

#### ب. في ملف `web/index.html` السطر 25:

**ابحث عن**:
```html
<meta name="google-signin-client_id" content="ضع_CLIENT_ID_هنا.apps.googleusercontent.com">
```

**استبدله بـ**:
```html
<meta name="google-signin-client_id" content="CLIENT_ID_الخاص_بك.apps.googleusercontent.com">
```

### 3. مثال كامل

إذا كان Client ID الخاص بك هو:
```
123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com
```

#### في auth_service_real_google.dart:
```dart
clientId: '123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com',
```

#### في index.html:
```html
<meta name="google-signin-client_id" content="123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com">
```

### 4. إعادة تشغيل التطبيق

```bash
flutter run -d chrome
```

### 5. اختبار Google Sign-In

1. اذهب لصفحة تسجيل الدخول
2. انقر "تسجيل الدخول بـ Google"
3. ستظهر نافذة Google الحقيقية
4. سجل دخول بحسابك الشخصي

## 🎯 النتيجة المتوقعة

- ✅ نافذة Google الحقيقية
- ✅ تسجيل دخول بحسابك الشخصي
- ✅ عرض بياناتك الحقيقية (اسم، بريد، صورة)
- ✅ عمل المصادقة بشكل مثالي

## 🔧 حل المشاكل

### خطأ "OAuth client was not found":
- تأكد من نسخ Client ID بشكل صحيح
- تأكد من عدم وجود مسافات إضافية
- تأكد من تحديث كلا الملفين

### خطأ "redirect_uri_mismatch":
- تأكد من إضافة `http://localhost:55101` في Google Console
- تأكد من استخدام نفس المنفذ (port)

### خطأ "popup_blocked":
- اسمح بالنوافذ المنبثقة في المتصفح
- جرب متصفح آخر

---

**💡 نصيحة**: احتفظ بـ Client ID في مكان آمن ولا تشاركه في repositories عامة.
