rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // دالة للتحقق من أن المستخدم مسجل دخول
    function isAuthenticated() {
      return request.auth != null;
    }

    // دالة للتحقق من أن المستخدم هو المالك للمورد
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }

    // دالة للتحقق من أن المستخدم مدير
    function isAdmin() {
      return request.auth != null &&
             exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }

    // قواعد للمنتجات - قراءة للجميع، كتابة للمدراء فقط
    match /products/{productId} {
      allow read: if true;
      allow write, delete: if isAdmin();
      allow create: if isAdmin();
    }

    // قواعد للفئات - قراءة للجميع، كتابة للمدراء فقط
    match /categories/{categoryId} {
      allow read: if true;
      allow write, delete: if isAdmin();
      allow create: if isAdmin();
    }

    // قواعد للعلامات التجارية - قراءة للجميع، كتابة للمدراء فقط
    match /brands/{brandId} {
      allow read: if true;
      allow write, delete: if isAdmin();
      allow create: if isAdmin();
    }

    // قواعد للمستخدمين - كل مستخدم يمكنه قراءة وتعديل بياناته فقط
    match /users/{userId} {
      allow read, write: if isOwner(userId);
      allow create: if isAuthenticated();
      allow read: if isAdmin(); // المدراء يمكنهم قراءة جميع المستخدمين
    }

    // قواعد للمدراء - قراءة للمدراء فقط
    match /admins/{adminId} {
      allow read: if isAdmin();
      allow write: if false; // لا يمكن إضافة مدراء من التطبيق
    }

    // قواعد للطلبات - المستخدم يمكنه قراءة طلباته فقط، المدراء يمكنهم قراءة وتعديل جميع الطلبات
    match /orders/{orderId} {
      allow read, write: if isAuthenticated() &&
                         (resource.data.userId == request.auth.uid || isAdmin());
      allow create: if isAuthenticated();
      allow delete: if isAdmin();
    }

    // قواعد للإشعارات - المستخدم يمكنه قراءة إشعاراته فقط
    match /notifications/{notificationId} {
      allow read: if isAuthenticated() &&
                  (resource.data.userId == request.auth.uid || isAdmin());
      allow write: if isAdmin();
      allow create: if isAdmin();
    }

    // قواعد للعناوين - المستخدم يمكنه إدارة عناوينه فقط
    match /addresses/{addressId} {
      allow read, write: if isAuthenticated() &&
                         resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated();
    }

    // قواعد للمراجعات - قراءة للجميع، كتابة للمستخدمين المسجلين
    match /reviews/{reviewId} {
      allow read: if true;
      allow write: if isAuthenticated() &&
                   (resource.data.userId == request.auth.uid || isAdmin());
      allow create: if isAuthenticated();
      allow delete: if isAdmin();
    }

    // قواعد للإحصائيات - قراءة للمدراء فقط
    match /statistics/{statId} {
      allow read, write: if isAdmin();
    }

    // قواعد للإعدادات العامة - قراءة للجميع، كتابة للمدراء فقط
    match /settings/{settingId} {
      allow read: if true;
      allow write: if isAdmin();
    }
  }
}