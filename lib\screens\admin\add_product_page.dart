import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'dart:async';
import 'package:file_picker/file_picker.dart';
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';
import '../../app_properties.dart';
import '../../models/product.dart';
import '../../models/category.dart' as category_model;
import '../../api_service_mock.dart';
import '../../services/firebase_platform_service.dart';

class AddProductPage extends StatefulWidget {
  const AddProductPage({super.key});

  @override
  State<AddProductPage> createState() => _AddProductPageState();
}

class _AddProductPageState extends State<AddProductPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _stockController = TextEditingController();
  final _brandController = TextEditingController();
  final _skuController = TextEditingController();
  final _imageUrlController = TextEditingController();

  List<category_model.Category> _categories = [];
  List<Map<String, dynamic>> _brands = [];
  String? _selectedCategoryId;
  String? _selectedBrandId;
  bool _isLoading = false;
  bool _isActive = true;
  bool _isFeatured = false;

  // متغيرات الصورة
  Uint8List? _selectedImageBytes;
  String? _selectedImageName;
  String? _selectedImageType; // نوع الصورة (png/jpeg)
  bool _useImageUpload = true; // true للرفع المباشر، false لرابط

  @override
  void initState() {
    super.initState();
    _loadCategories();
    _loadBrands();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _stockController.dispose();
    _brandController.dispose();
    _skuController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    try {
      if (kDebugMode) print('🔄 تحميل الفئات من Firebase...');

      // محاولة جلب الفئات من Firebase Web Service أولاً
      try {
        final categoriesData = await FirebasePlatformService.getCategories();
        if (kDebugMode) {
          print('✅ تم جلب ${categoriesData.length} فئة من Firebase');
        }

        // تحويل البيانات إلى كائنات Category
        final categories = categoriesData
            .map((data) => category_model.Category.fromJson(data))
            .toList();

        setState(() {
          _categories = categories;
        });
        return;
      } catch (firebaseError) {
        if (kDebugMode) print('⚠️ فشل جلب الفئات من Firebase: $firebaseError');
      }

      // في حالة فشل Firebase، استخدم البيانات المحلية
      final categories = await ApiService.getCategories();
      if (kDebugMode) {
        print('📂 تم جلب ${categories.length} فئة من البيانات المحلية');
      }
      setState(() {
        _categories = categories;
      });
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تحميل الفئات: $e');
      // في حالة فشل كل شيء، استخدم فئات افتراضية من PredefinedCategories
      setState(() {
        _categories = category_model.PredefinedCategories.getCategories();
      });
    }
  }

  Future<void> _loadBrands() async {
    try {
      if (kDebugMode) print('🔄 تحميل العلامات التجارية من Firebase...');

      // محاولة جلب العلامات التجارية من Firebase Web Service
      try {
        final brandsData = await FirebasePlatformService.getBrands();
        if (kDebugMode) {
          print('✅ تم جلب ${brandsData.length} علامة تجارية من Firebase');
        }

        setState(() {
          _brands = brandsData;
        });
        return;
      } catch (firebaseError) {
        if (kDebugMode) {
          print('⚠️ فشل جلب العلامات التجارية من Firebase: $firebaseError');
        }
      }

      // في حالة فشل Firebase، استخدم قائمة فارغة
      setState(() {
        _brands = [];
      });
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تحميل العلامات التجارية: $e');
      setState(() {
        _brands = [];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('إضافة منتج جديد'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProduct,
            child: const Text(
              'حفظ',
              style: TextStyle(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات أساسية
              _buildSectionCard(
                title: 'المعلومات الأساسية',
                children: [
                  _buildTextField(
                    controller: _nameController,
                    label: 'اسم المنتج',
                    hint: 'أدخل اسم المنتج',
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال اسم المنتج';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _descriptionController,
                    label: 'وصف المنتج',
                    hint: 'أدخل وصف تفصيلي للمنتج',
                    maxLines: 4,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال وصف المنتج';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildBrandDropdown(),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _skuController,
                    label: 'رمز المنتج (SKU)',
                    hint: 'أدخل رمز المنتج الفريد',
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // السعر والمخزون
              _buildSectionCard(
                title: 'السعر والمخزون',
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: _priceController,
                          label: 'السعر (IQD)',
                          hint: '0',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال السعر';
                            }
                            if (double.tryParse(value) == null) {
                              return 'يرجى إدخال رقم صحيح';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTextField(
                          controller: _stockController,
                          label: 'الكمية المتوفرة',
                          hint: '0',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال الكمية';
                            }
                            if (int.tryParse(value) == null) {
                              return 'يرجى إدخال رقم صحيح';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // الصور
              _buildSectionCard(
                title: 'صور المنتج',
                children: [_buildImageSection()],
              ),

              const SizedBox(height: 20),

              // الفئة والإعدادات
              _buildSectionCard(
                title: 'الفئة والإعدادات',
                children: [
                  _buildCategoryDropdown(),
                  const SizedBox(height: 16),
                  _buildSwitchTile(
                    title: 'منتج نشط',
                    subtitle: 'سيظهر المنتج للعملاء',
                    value: _isActive,
                    onChanged: (value) => setState(() => _isActive = value),
                  ),
                  _buildSwitchTile(
                    title: 'منتج مميز',
                    subtitle: 'سيظهر في قسم المنتجات المميزة',
                    value: _isFeatured,
                    onChanged: (value) => setState(() => _isFeatured = value),
                  ),
                ],
              ),

              const SizedBox(height: 32),

              // أزرار الحفظ والإلغاء
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading
                          ? null
                          : () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveProduct,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryColor,
                        foregroundColor: AppColors.white,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  AppColors.white,
                                ),
                              ),
                            )
                          : const Text('حفظ المنتج'),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: const OutlineInputBorder(),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primaryColor),
        ),
      ),
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
    );
  }

  Widget _buildCategoryDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedCategoryId,
      decoration: const InputDecoration(
        labelText: 'الفئة',
        border: OutlineInputBorder(),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primaryColor),
        ),
      ),
      hint: const Text('اختر فئة المنتج'),
      items: _categories.map((category) {
        return DropdownMenuItem(value: category.id, child: Text(category.name));
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedCategoryId = value;
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى اختيار فئة المنتج';
        }
        return null;
      },
    );
  }

  Widget _buildBrandDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedBrandId,
      decoration: const InputDecoration(
        labelText: 'العلامة التجارية',
        border: OutlineInputBorder(),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primaryColor),
        ),
      ),
      hint: const Text('اختر العلامة التجارية'),
      items: _brands.map((brand) {
        return DropdownMenuItem(
          value: brand['id'].toString(),
          child: Text(brand['name'] ?? 'غير محدد'),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedBrandId = value;
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى اختيار العلامة التجارية';
        }
        return null;
      },
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
      subtitle: Text(
        subtitle,
        style: const TextStyle(color: AppColors.secondaryText, fontSize: 12),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: AppColors.primaryColor,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // خيارات رفع الصورة
        Row(
          children: [
            Expanded(
              child: RadioListTile<bool>(
                title: const Text('رفع صورة مباشر'),
                subtitle: const Text('PNG, JPEG'),
                value: true,
                groupValue: _useImageUpload,
                onChanged: (value) {
                  setState(() {
                    _useImageUpload = value!;
                    if (_useImageUpload) {
                      _imageUrlController.clear();
                    } else {
                      _selectedImageBytes = null;
                      _selectedImageName = null;
                      _selectedImageType = null;
                    }
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
            ),
            Expanded(
              child: RadioListTile<bool>(
                title: const Text('رابط صورة'),
                subtitle: const Text('URL'),
                value: false,
                groupValue: _useImageUpload,
                onChanged: (value) {
                  setState(() {
                    _useImageUpload = value!;
                    if (_useImageUpload) {
                      _imageUrlController.clear();
                    } else {
                      _selectedImageBytes = null;
                      _selectedImageName = null;
                      _selectedImageType = null;
                    }
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // قسم رفع الصورة المباشر
        if (_useImageUpload) ...[
          _buildImageUploadSection(),
        ] else ...[
          _buildImageUrlSection(),
        ],
      ],
    );
  }

  Widget _buildImageUploadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // زر رفع الصورة
        Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(
              color: _selectedImageBytes != null
                  ? AppColors.primaryColor
                  : AppColors.grey.withValues(alpha: 0.3),
              width: 2,
              style: BorderStyle.solid,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: _selectedImageBytes != null
              ? Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: Image.memory(
                        _selectedImageBytes!,
                        width: double.infinity,
                        height: double.infinity,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        decoration: const BoxDecoration(
                          color: AppColors.errorColor,
                          shape: BoxShape.circle,
                        ),
                        child: IconButton(
                          icon: const Icon(
                            Icons.close,
                            color: AppColors.white,
                            size: 20,
                          ),
                          onPressed: () {
                            setState(() {
                              _selectedImageBytes = null;
                              _selectedImageName = null;
                              _selectedImageType = null;
                            });
                          },
                        ),
                      ),
                    ),
                  ],
                )
              : InkWell(
                  onTap: _pickImage,
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.cloud_upload,
                        size: 48,
                        color: AppColors.primaryColor,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'اضغط لرفع صورة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: AppColors.primaryColor,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'PNG, JPEG - حد أقصى 5MB',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.secondaryText,
                        ),
                      ),
                    ],
                  ),
                ),
        ),

        if (_selectedImageName != null) ...[
          const SizedBox(height: 8),
          Text(
            'الملف المختار: $_selectedImageName',
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.secondaryText,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildImageUrlSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextField(
          controller: _imageUrlController,
          label: 'رابط الصورة الرئيسية',
          hint: 'https://example.com/image.jpg',
          validator: (value) {
            if (!_useImageUpload && (value == null || value.trim().isEmpty)) {
              return 'يرجى إدخال رابط الصورة';
            }
            if (!_useImageUpload && value != null && value.isNotEmpty) {
              final uri = Uri.tryParse(value);
              if (uri == null || !uri.hasAbsolutePath) {
                return 'يرجى إدخال رابط صحيح';
              }
            }
            return null;
          },
        ),
        const SizedBox(height: 12),
        const Text(
          'أمثلة على روابط الصور:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.primaryText,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.lightGrey.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '• https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=500&h=500',
                style: TextStyle(fontSize: 12, color: AppColors.secondaryText),
              ),
              SizedBox(height: 4),
              Text(
                '• https://example.com/product-image.jpg',
                style: TextStyle(fontSize: 12, color: AppColors.secondaryText),
              ),
              SizedBox(height: 4),
              Text(
                '• تأكد من أن الرابط يؤدي مباشرة للصورة',
                style: TextStyle(fontSize: 12, color: AppColors.warningColor),
              ),
            ],
          ),
        ),
        if (_imageUrlController.text.isNotEmpty) ...[
          const SizedBox(height: 16),
          const Text(
            'معاينة الصورة:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.primaryText,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.grey.withValues(alpha: 0.3)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(7),
              child: Image.network(
                _imageUrlController.text,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error,
                          color: AppColors.errorColor,
                          size: 40,
                        ),
                        SizedBox(height: 8),
                        Text(
                          'فشل في تحميل الصورة',
                          style: TextStyle(color: AppColors.errorColor),
                        ),
                      ],
                    ),
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return const Center(child: CircularProgressIndicator());
                },
              ),
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _pickImage() async {
    try {
      if (kDebugMode) {
        debugPrint('🔄 بدء عملية اختيار الصورة...');
      }

      // للويب: استخدم image_picker بدلاً من file_picker
      if (kIsWeb) {
        await _pickImageWithImagePicker();
        return;
      }

      // للمنصات الأخرى: استخدم FilePicker العادي
      final result = await FilePicker.platform
          .pickFiles(
            type: FileType.custom,
            allowedExtensions: ['png', 'jpg', 'jpeg'],
            withData: true,
            allowMultiple: false,
            dialogTitle: 'اختر صورة المنتج',
          )
          .timeout(
            const Duration(seconds: 30),
            onTimeout: () {
              throw TimeoutException(
                'انتهت مهلة اختيار الملف',
                const Duration(seconds: 30),
              );
            },
          );

      if (kDebugMode) {
        debugPrint('📁 نتيجة FilePicker: ${result != null ? 'نجح' : 'فشل'}');
      }

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        if (kDebugMode) {
          debugPrint('📄 تفاصيل الملف:');
          debugPrint('  - الاسم: ${file.name}');
          debugPrint('  - الحجم: ${file.size} بايت');
          debugPrint('  - الامتداد: ${file.extension}');
          debugPrint('  - البيانات متاحة: ${file.bytes != null}');
        }

        // التحقق من وجود البيانات
        if (file.bytes == null) {
          if (kDebugMode) {
            debugPrint('❌ بيانات الملف غير متاحة');
          }
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('فشل في قراءة بيانات الصورة'),
                backgroundColor: AppColors.errorColor,
              ),
            );
          }
          return;
        }

        // التحقق من حجم الملف (2MB حد أقصى لتجنب مشاكل Firestore)
        if (file.size > 2 * 1024 * 1024) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('حجم الصورة كبير جداً. الحد الأقصى 2MB'),
                backgroundColor: AppColors.errorColor,
              ),
            );
          }
          return;
        }

        // تحديد نوع الصورة من الامتداد
        String imageType = 'jpeg'; // افتراضي
        if (file.extension?.toLowerCase() == 'png') {
          imageType = 'png';
        } else if (file.extension?.toLowerCase() == 'jpg' ||
            file.extension?.toLowerCase() == 'jpeg') {
          imageType = 'jpeg';
        }

        // ضغط الصورة إذا كانت JPEG لتقليل الحجم
        Uint8List? processedBytes = file.bytes;
        if (imageType == 'jpeg' && file.bytes != null) {
          try {
            // تقليل جودة JPEG لتوفير المساحة
            processedBytes = await _compressJpegImage(file.bytes!);
            if (kDebugMode) {
              debugPrint(
                '✅ تم ضغط صورة JPEG من ${file.size} إلى ${processedBytes!.length} بايت',
              );
            }
          } catch (e) {
            if (kDebugMode) {
              debugPrint('⚠️ فشل ضغط الصورة، استخدام الأصلية: $e');
            }
            processedBytes = file.bytes;
          }
        }

        if (kDebugMode) {
          debugPrint('✅ تم اختيار الصورة بنجاح');
        }

        setState(() {
          _selectedImageBytes = processedBytes;
          _selectedImageName = file.name;
          _selectedImageType = imageType;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم اختيار الصورة: ${file.name}'),
              backgroundColor: AppColors.primaryColor,
            ),
          );
        }
      } else {
        if (kDebugMode) {
          debugPrint('❌ لم يتم اختيار أي ملف');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في اختيار الصورة: $e');
        debugPrint('❌ نوع الخطأ: ${e.runtimeType}');
        debugPrint('❌ تفاصيل الخطأ: ${e.toString()}');
      }

      String errorMessage = 'فشل في اختيار الصورة';

      if (e.toString().contains('LateInitializationError')) {
        errorMessage =
            'خطأ في تهيئة النظام. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.';
      } else if (e.toString().contains('TimeoutException')) {
        errorMessage = 'انتهت مهلة اختيار الملف. يرجى المحاولة مرة أخرى.';
      } else if (e.toString().contains('PlatformException')) {
        errorMessage = 'خطأ في النظام. تأكد من أن المتصفح يدعم رفع الملفات.';
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: AppColors.errorColor,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _pickImage(),
            ),
          ),
        );
      }
    }
  }

  Future<void> _pickImageWithImagePicker() async {
    try {
      if (kDebugMode) {
        debugPrint('🌐 استخدام ImagePicker لاختيار الصورة...');
      }

      final ImagePicker picker = ImagePicker();

      // اختيار صورة من المعرض
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        if (kDebugMode) {
          debugPrint('📄 تم اختيار الصورة: ${image.name}');
          debugPrint('📏 حجم الملف: ${await image.length()} بايت');
        }

        // قراءة بيانات الصورة
        final bytes = await image.readAsBytes();

        // التحقق من حجم الملف
        if (bytes.length > 2 * 1024 * 1024) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('حجم الصورة كبير جداً. الحد الأقصى 2MB'),
                backgroundColor: AppColors.errorColor,
              ),
            );
          }
          return;
        }

        // تحديد نوع الصورة من الاسم
        String imageType = 'jpeg';
        if (image.name.toLowerCase().endsWith('.png')) {
          imageType = 'png';
        }

        // ضغط الصورة إذا كانت JPEG
        Uint8List? processedBytes = bytes;
        if (imageType == 'jpeg') {
          try {
            processedBytes = await _compressJpegImage(bytes);
            if (kDebugMode) {
              debugPrint(
                '✅ تم ضغط صورة JPEG من ${bytes.length} إلى ${processedBytes!.length} بايت',
              );
            }
          } catch (e) {
            if (kDebugMode) {
              debugPrint('⚠️ فشل ضغط الصورة، استخدام الأصلية: $e');
            }
            processedBytes = bytes;
          }
        }

        setState(() {
          _selectedImageBytes = processedBytes;
          _selectedImageName = image.name;
          _selectedImageType = imageType;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم اختيار الصورة: ${image.name}'),
              backgroundColor: AppColors.primaryColor,
            ),
          );
        }
      } else {
        if (kDebugMode) {
          debugPrint('❌ لم يتم اختيار أي صورة');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في ImagePicker: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في اختيار الصورة: ${e.toString()}'),
            backgroundColor: AppColors.errorColor,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _pickImageWithImagePicker(),
            ),
          ),
        );
      }
    }
  }

  Future<void> _pickImageForWeb() async {
    try {
      if (kDebugMode) {
        debugPrint('🌐 استخدام HTML Input مباشر لاختيار الصورة...');
      }

      // استدعاء دالة JavaScript مباشرة
      final result = await _callHtmlInputPickFile();

      if (result != null) {
        final name = result['name'] as String;
        final size = result['size'] as int;
        final dataUrl = result['data'] as String;

        if (kDebugMode) {
          debugPrint('📄 تفاصيل الملف:');
          debugPrint('  - الاسم: $name');
          debugPrint('  - الحجم: $size بايت');
        }

        // التحقق من حجم الملف
        if (size > 2 * 1024 * 1024) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('حجم الصورة كبير جداً. الحد الأقصى 2MB'),
                backgroundColor: AppColors.errorColor,
              ),
            );
          }
          return;
        }

        // استخراج البيانات من Data URL
        final base64Data = dataUrl.split(',')[1];
        final bytes = base64Decode(base64Data);

        // تحديد نوع الصورة
        String imageType = 'jpeg';
        if (name.toLowerCase().endsWith('.png')) {
          imageType = 'png';
        }

        // ضغط الصورة إذا كانت JPEG
        Uint8List? processedBytes = bytes;
        if (imageType == 'jpeg') {
          try {
            processedBytes = await _compressJpegImage(bytes);
            if (kDebugMode) {
              debugPrint(
                '✅ تم ضغط صورة JPEG من $size إلى ${processedBytes!.length} بايت',
              );
            }
          } catch (e) {
            if (kDebugMode) {
              debugPrint('⚠️ فشل ضغط الصورة، استخدام الأصلية: $e');
            }
            processedBytes = bytes;
          }
        }

        setState(() {
          _selectedImageBytes = processedBytes;
          _selectedImageName = name;
          _selectedImageType = imageType;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم اختيار الصورة: $name'),
              backgroundColor: AppColors.primaryColor,
            ),
          );
        }
      } else {
        // لم يتم اختيار أي ملف أو فشل FilePicker
        if (kDebugMode) {
          debugPrint('❌ لم يتم اختيار أي ملف أو فشل FilePicker');
        }
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'لم يتم اختيار أي صورة. يرجى المحاولة مرة أخرى.',
              ),
              backgroundColor: AppColors.warningColor,
              action: SnackBarAction(
                label: 'إعادة المحاولة',
                textColor: Colors.white,
                onPressed: () => _pickImageForWeb(),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في اختيار الصورة عبر JavaScript: $e');
      }

      String errorMessage = 'فشل في اختيار الصورة';
      if (e.toString().contains('LateInitializationError')) {
        errorMessage =
            'خطأ في تهيئة النظام. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.';
      } else if (e.toString().contains('TimeoutException')) {
        errorMessage = 'انتهت مهلة اختيار الملف. يرجى المحاولة مرة أخرى.';
      } else if (e.toString().contains('PlatformException')) {
        errorMessage = 'خطأ في النظام. تأكد من أن المتصفح يدعم رفع الملفات.';
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: AppColors.errorColor,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _pickImageForWeb(),
            ),
          ),
        );
      }
    }
  }

  Future<Map<String, dynamic>?> _callHtmlInputPickFile() async {
    try {
      if (kDebugMode) {
        debugPrint('🌐 محاولة استخدام FilePicker مع معالجة محسنة...');
      }

      // تأخير أطول للتهيئة في الويب
      if (kIsWeb) {
        await Future.delayed(const Duration(milliseconds: 2000));
      }

      // محاولة استخدام FilePicker مع معالجة شاملة للأخطاء
      FilePickerResult? result;

      try {
        result = await FilePicker.platform
            .pickFiles(
              type: FileType.custom,
              allowedExtensions: ['png', 'jpg', 'jpeg'],
              withData: true,
              allowMultiple: false,
              dialogTitle: 'اختر صورة المنتج',
            )
            .timeout(
              const Duration(seconds: 45),
              onTimeout: () {
                if (kDebugMode) {
                  debugPrint('⏰ انتهت مهلة FilePicker');
                }
                return null;
              },
            );
      } on Exception catch (e) {
        if (kDebugMode) {
          debugPrint('❌ خطأ في FilePicker: $e');
        }

        // إذا كان الخطأ LateInitializationError، أرجع null
        if (e.toString().contains('LateInitializationError')) {
          if (kDebugMode) {
            debugPrint('🔄 خطأ LateInitializationError - سيتم إرجاع null');
          }
          return null;
        }

        // للأخطاء الأخرى، أعد رمي الخطأ
        rethrow;
      }

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.bytes != null) {
          final base64Data = base64Encode(file.bytes!);
          return {
            'name': file.name,
            'size': file.size,
            'data': 'data:${_getMimeType(file.extension)};base64,$base64Data',
          };
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ عام في اختيار الملف: $e');
      }
      return null;
    }
  }

  String _getMimeType(String? extension) {
    switch (extension?.toLowerCase()) {
      case 'png':
        return 'image/png';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      default:
        return 'image/jpeg';
    }
  }

  Future<Uint8List?> _compressJpegImage(Uint8List imageBytes) async {
    try {
      // استخدام مكتبة image لضغط الصورة
      final image = img.decodeImage(imageBytes);
      if (image == null) return imageBytes;

      // تقليل الحجم إذا كان كبيراً
      img.Image resizedImage = image;
      if (image.width > 800 || image.height > 800) {
        resizedImage = img.copyResize(image, width: 800);
      }

      // ضغط بجودة 70%
      final compressedBytes = img.encodeJpg(resizedImage, quality: 70);
      return Uint8List.fromList(compressedBytes);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في ضغط الصورة: $e');
      }
      return imageBytes;
    }
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من وجود صورة
    if (_useImageUpload && _selectedImageBytes == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار صورة للمنتج'),
          backgroundColor: AppColors.errorColor,
        ),
      );
      return;
    }

    if (!_useImageUpload && _imageUrlController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال رابط الصورة'),
          backgroundColor: AppColors.errorColor,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // البحث عن اسم الفئة
      final selectedCategory = _categories.firstWhere(
        (cat) => cat.id == _selectedCategoryId,
        orElse: () => _categories.first,
      );

      // البحث عن اسم العلامة التجارية
      String? selectedBrandName;
      if (_selectedBrandId != null) {
        final selectedBrand = _brands.firstWhere(
          (brand) => brand['id'].toString() == _selectedBrandId,
          orElse: () => <String, dynamic>{},
        );
        selectedBrandName = selectedBrand['name'];
      }

      // تحديد مصدر الصورة
      String imageUrl = '';
      if (_useImageUpload && _selectedImageBytes != null) {
        // تحويل الصورة إلى Base64 مع نوع الصورة الصحيح
        final mimeType = _selectedImageType ?? 'jpeg';
        imageUrl =
            'data:image/$mimeType;base64,${base64Encode(_selectedImageBytes!)}';
      } else if (!_useImageUpload) {
        imageUrl = _imageUrlController.text.trim();
      }

      // إنشاء منتج جديد
      final newProduct = Product(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        price: double.parse(_priceController.text),
        originalPrice: double.parse(_priceController.text),
        image: imageUrl, // الصورة الرئيسية
        images: imageUrl.isNotEmpty ? [imageUrl] : [], // الصور الإضافية
        categoryId: _selectedCategoryId!,
        categoryName: selectedCategory.name,
        rating: 0.0,
        reviewsCount: 0,
        inStock: int.parse(_stockController.text) > 0,
        stockQuantity: int.parse(_stockController.text),
        specifications: {},
        brand: selectedBrandName ?? _brandController.text.trim(),
        type: ProductType.eyeglasses, // افتراضي، يمكن تحسينه لاحقاً
        stock: int.parse(_stockController.text),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isFeatured: _isFeatured,
        tags: _skuController.text.trim().isEmpty
            ? null
            : _skuController.text.trim(),
      );

      // محاكاة حفظ المنتج
      await Future.delayed(const Duration(seconds: 1));

      // حفظ المنتج في Firebase والبيانات المحلية
      try {
        if (kDebugMode) {
          debugPrint('🔄 حفظ المنتج في Firebase...');
        }

        // استخدام Firebase Platform Service مع كائن Product
        final success = await FirebasePlatformService.addProduct(newProduct);

        if (success) {
          if (kDebugMode) {
            debugPrint('✅ تم حفظ المنتج في Firebase بنجاح');
          }
        } else {
          throw Exception('فشل في حفظ المنتج في Firebase');
        }
      } catch (e) {
        if (kDebugMode) {
          debugPrint('❌ خطأ في حفظ المنتج في Firebase: $e');
        }
        // في حالة فشل Firebase، احفظ محلياً
        try {
          await ApiService.addProduct(newProduct);
          if (kDebugMode) {
            debugPrint('📂 تم حفظ المنتج محلياً كبديل');
          }
        } catch (localError) {
          if (kDebugMode) {
            debugPrint('❌ فشل في الحفظ المحلي أيضاً: $localError');
          }
          throw Exception('فشل في حفظ المنتج');
        }
      }
      if (kDebugMode) {
        debugPrint('تم إنشاء منتج جديد: ${newProduct.name}');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة المنتج بنجاح - سيتم إعادة تحميل الصفحة'),
            backgroundColor: AppColors.successColor,
            duration: Duration(seconds: 2),
          ),
        );

        // انتظار قصير ثم إعادة تحميل الصفحة لجلب البيانات المحدثة
        await Future.delayed(const Duration(seconds: 2));

        // إعادة تحميل الصفحة تلقائياً
        if (mounted) {
          Navigator.of(context).pop();
        } else {
          if (mounted) {
            Navigator.pop(context, true); // إرجاع true للإشارة إلى نجاح الإضافة
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في حفظ المنتج: $e');
      }

      // تحقق إذا كان الخطأ متعلق بإعادة التحميل (يمكن تجاهله)
      final errorString = e.toString().toLowerCase();
      final isReloadError =
          errorString.contains('reload') ||
          errorString.contains('location') ||
          errorString.contains('navigation');

      if (!isReloadError && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء حفظ المنتج'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
