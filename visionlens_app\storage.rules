rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // دالة للتحقق من أن المستخدم مسجل دخول
    function isAuthenticated() {
      return request.auth != null;
    }

    // دالة للتحقق من أن المستخدم مدير
    function isAdmin() {
      return request.auth != null &&
             firestore.exists(/databases/(default)/documents/admins/$(request.auth.uid));
    }

    // دالة للتحقق من صحة نوع الملف
    function isValidImageType() {
      return request.resource.contentType.matches('image/.*');
    }

    // دالة للتحقق من حجم الملف (أقل من 5 ميجابايت)
    function isValidSize() {
      return request.resource.size < 5 * 1024 * 1024;
    }

    // صور المنتجات - قراءة للجميع، كتابة للمدراء فقط
    match /products/{productId}/{imageId} {
      allow read: if true;
      allow write, delete: if isAdmin() && isValidImageType() && isValidSize();
    }

    // صور الفئات - قراءة للجميع، كتابة للمدراء فقط
    match /categories/{categoryId}/{imageId} {
      allow read: if true;
      allow write, delete: if isAdmin() && isValidImageType() && isValidSize();
    }

    // صور العلامات التجارية - قراءة للجميع، كتابة للمدراء فقط
    match /brands/{brandId}/{imageId} {
      allow read: if true;
      allow write, delete: if isAdmin() && isValidImageType() && isValidSize();
    }

    // صور المستخدمين - كل مستخدم يمكنه إدارة صوره فقط
    match /users/{userId}/{imageId} {
      allow read: if true;
      allow write, delete: if isAuthenticated() &&
                           (request.auth.uid == userId || isAdmin()) &&
                           isValidImageType() && isValidSize();
    }

    // صور عامة - قراءة للجميع، كتابة للمدراء فقط
    match /public/{imageId} {
      allow read: if true;
      allow write, delete: if isAdmin() && isValidImageType() && isValidSize();
    }

    // مجلد مؤقت للرفع - للمستخدمين المسجلين فقط
    match /temp/{userId}/{imageId} {
      allow read, write, delete: if isAuthenticated() &&
                                 request.auth.uid == userId &&
                                 isValidImageType() && isValidSize();
    }

    // منع الوصول لأي مسارات أخرى
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
