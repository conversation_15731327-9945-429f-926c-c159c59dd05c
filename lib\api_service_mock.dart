import 'package:shared_preferences/shared_preferences.dart';
import 'models/product.dart';
import 'models/user_simple.dart';
import 'models/category.dart';
import 'models/order.dart';
import 'models/review.dart';
import 'app_properties.dart';
import 'services/data_service.dart';

class ApiService {
  static const String baseUrl = AppConstants.baseUrl;
  static const Duration timeoutDuration = Duration(seconds: 30);

  // حفظ التوكن في التخزين المحلي
  static Future<void> _saveAuthToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.userTokenKey, token);
  }

  // إزالة التوكن من التخزين المحلي
  static Future<void> _removeAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.userTokenKey);
  }

  // ==================== خدمات المصادقة ====================

  // تسجيل الدخول
  static Future<AuthResponse> login(String email, String password) async {
    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // محاكاة تسجيل الدخول
      if (email.isNotEmpty && password.isNotEmpty) {
        final token = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
        await _saveAuthToken(token);

        final user = User(
          id: '1',
          email: email,
          firstName: 'مستخدم',
          lastName: 'VisionLens',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isEmailVerified: true,
        );

        return AuthResponse(user: user, token: token);
      } else {
        throw ApiException(
          message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(message: 'حدث خطأ في تسجيل الدخول');
    }
  }

  // التسجيل
  static Future<AuthResponse> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 2));

      // محاكاة التسجيل
      if (email.isNotEmpty &&
          password.isNotEmpty &&
          firstName.isNotEmpty &&
          lastName.isNotEmpty) {
        final token = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
        await _saveAuthToken(token);

        final user = User(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          email: email,
          firstName: firstName,
          lastName: lastName,
          phone: phone,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isEmailVerified: true,
          isPhoneVerified: phone != null,
        );

        return AuthResponse(user: user, token: token);
      } else {
        throw ApiException(message: 'يرجى ملء جميع الحقول المطلوبة');
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(message: 'حدث خطأ في التسجيل');
    }
  }

  // إرسال رمز التحقق
  static Future<void> sendOTP(String phone) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      // محاكاة إرسال OTP
    } catch (e) {
      throw ApiException(message: 'فشل في إرسال رمز التحقق');
    }
  }

  // التحقق من رمز OTP
  static Future<void> verifyOTP(String phone, String otp) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      // محاكاة التحقق من OTP
      if (otp != '1234') {
        throw ApiException(message: 'رمز التحقق غير صحيح');
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(message: 'فشل في التحقق من الرمز');
    }
  }

  // نسيان كلمة المرور
  static Future<void> forgotPassword(String email) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      // محاكاة إرسال رابط إعادة تعيين كلمة المرور
    } catch (e) {
      throw ApiException(message: 'فشل في إرسال رابط إعادة التعيين');
    }
  }

  // تسجيل الخروج
  static Future<void> logout() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      await _removeAuthToken();
    } catch (e) {
      // تجاهل الأخطاء في تسجيل الخروج
      await _removeAuthToken();
    }
  }

  // ==================== خدمات المنتجات ====================

  // جلب المنتجات
  static Future<List<Product>> getProducts({
    int page = 1,
    int limit = 20,
    String? categoryId,
    String? search,
    String? sortBy,
    double? minPrice,
    double? maxPrice,
  }) async {
    try {
      await Future.delayed(const Duration(seconds: 1));

      List<Product> products;

      if (categoryId != null) {
        products = await DataService.getProductsByCategory(categoryId);
      } else if (search != null && search.isNotEmpty) {
        products = await DataService.searchProducts(search);
      } else {
        products = await DataService.getProducts();
      }

      // تطبيق فلاتر السعر
      if (minPrice != null) {
        products = products.where((p) => p.price >= minPrice).toList();
      }
      if (maxPrice != null) {
        products = products.where((p) => p.price <= maxPrice).toList();
      }

      // تطبيق الترتيب
      if (sortBy != null) {
        switch (sortBy) {
          case 'price_low':
            products.sort((a, b) => a.price.compareTo(b.price));
            break;
          case 'price_high':
            products.sort((a, b) => b.price.compareTo(a.price));
            break;
          case 'name':
            products.sort((a, b) => a.name.compareTo(b.name));
            break;
          case 'newest':
            products.sort((a, b) => b.createdAt.compareTo(a.createdAt));
            break;
        }
      }

      // تطبيق التصفح
      final startIndex = (page - 1) * limit;
      final endIndex = startIndex + limit;

      if (startIndex >= products.length) {
        return [];
      }

      return products.sublist(
        startIndex,
        endIndex > products.length ? products.length : endIndex,
      );
    } catch (e) {
      throw ApiException(message: 'فشل في جلب المنتجات');
    }
  }

  // جلب منتج بالمعرف
  static Future<Product> getProductById(String id) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));

      return _generateMockProducts(1).first;
    } catch (e) {
      throw ApiException(message: 'فشل في جلب تفاصيل المنتج');
    }
  }

  // جلب المنتجات المميزة
  static Future<List<Product>> getFeaturedProducts() async {
    try {
      await Future.delayed(const Duration(milliseconds: 800));

      return await DataService.getFeaturedProducts();
    } catch (e) {
      throw ApiException(message: 'فشل في جلب المنتجات المميزة');
    }
  }

  // ==================== خدمات الفئات ====================

  // جلب الفئات
  static Future<List<Category>> getCategories() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      return await DataService.getCategories();
    } catch (e) {
      throw ApiException(message: 'فشل في جلب الفئات');
    }
  }

  // ==================== خدمات المستخدم ====================

  // جلب بيانات المستخدم الحالي
  static Future<User> getCurrentUser() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));

      final user = User(
        id: '1',
        email: '<EMAIL>',
        firstName: 'مستخدم',
        lastName: 'VisionLens',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isEmailVerified: true,
      );

      return user;
    } catch (e) {
      throw ApiException(message: 'فشل في جلب بيانات المستخدم');
    }
  }

  // تحديث بيانات المستخدم
  static Future<User> updateUser(Map<String, dynamic> userData) async {
    try {
      await Future.delayed(const Duration(seconds: 1));

      final user = User(
        id: userData['id'] ?? '1',
        email: userData['email'] ?? '<EMAIL>',
        firstName: userData['firstName'] ?? 'مستخدم',
        lastName: userData['lastName'] ?? 'VisionLens',
        phone: userData['phone'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isEmailVerified: true,
      );

      return user;
    } catch (e) {
      throw ApiException(message: 'فشل في تحديث بيانات المستخدم');
    }
  }

  // ==================== خدمات الفئات ====================

  // إضافة فئة جديدة
  static Future<void> addCategory(Category category) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      await DataService.addCategory(category);
    } catch (e) {
      throw ApiException(message: 'فشل في إضافة الفئة');
    }
  }

  // تحديث فئة
  static Future<void> updateCategory(Category category) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      await DataService.updateCategory(category);
    } catch (e) {
      throw ApiException(message: 'فشل في تحديث الفئة');
    }
  }

  // حذف فئة
  static Future<void> deleteCategory(String categoryId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      await DataService.deleteCategory(categoryId);
    } catch (e) {
      throw ApiException(message: 'فشل في حذف الفئة');
    }
  }

  // ==================== خدمات الطلبات ====================

  // جلب الطلبات
  static Future<List<Order>> getOrders() async {
    try {
      await Future.delayed(const Duration(milliseconds: 800));
      // final ordersData = await DataService.getOrders(); // غير مستخدم
      // تحويل البيانات إلى Order objects إذا لزم الأمر
      return _generateMockOrders();
    } catch (e) {
      throw ApiException(message: 'فشل في جلب الطلبات');
    }
  }

  // حذف منتج
  static Future<void> deleteProduct(String productId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      await DataService.deleteProduct(productId);
    } catch (e) {
      throw ApiException(message: 'فشل في حذف المنتج');
    }
  }

  // قائمة الطلبات المحفوظة (فارغة في البداية - سيتم إضافة الطلبات من العملاء الحقيقيين)
  static final List<Order> _savedOrders = [];

  // إرجاع الطلبات المحفوظة
  static List<Order> _generateMockOrders() {
    return _savedOrders;
  }

  // إضافة منتج جديد
  static Future<void> addProduct(Product product) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      await DataService.addProduct(product);
    } catch (e) {
      throw ApiException(message: 'فشل في إضافة المنتج');
    }
  }

  // تحديث منتج
  static Future<void> updateProduct(Product product) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      await DataService.updateProduct(product);
    } catch (e) {
      throw ApiException(message: 'فشل في تحديث المنتج');
    }
  }

  // إرجاع المنتجات المحفوظة (فارغة في البداية - سيتم إضافة المنتجات من لوحة الإدارة)
  static List<Product> _generateMockProducts(int count) {
    // إرجاع قائمة فارغة - سيتم ملؤها من DataService
    return [];
  }

  // إضافة طلب جديد
  static Future<void> addOrder(Order order) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      _savedOrders.insert(0, order); // إضافة في المقدمة
    } catch (e) {
      throw ApiException(message: 'فشل في إضافة الطلب');
    }
  }

  // ==================== خدمات التقييمات ====================

  // قائمة التقييمات المحفوظة
  static final List<Review> _savedReviews = [];

  // جلب تقييمات منتج معين
  static Future<List<Review>> getProductReviews(String productId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));

      // فلترة التقييمات حسب معرف المنتج
      final productReviews = _savedReviews
          .where((review) => review.productId == productId)
          .toList();

      // ترتيب حسب التاريخ (الأحدث أولاً)
      productReviews.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return productReviews;
    } catch (e) {
      throw ApiException(message: 'فشل في جلب التقييمات');
    }
  }

  // إضافة تقييم جديد
  static Future<void> addReview(Review review) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      _savedReviews.insert(0, review); // إضافة في المقدمة
    } catch (e) {
      throw ApiException(message: 'فشل في إضافة التقييم');
    }
  }

  // تحديث تقييم
  static Future<void> updateReview(Review review) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));

      final index = _savedReviews.indexWhere((r) => r.id == review.id);
      if (index != -1) {
        _savedReviews[index] = review;
      }
    } catch (e) {
      throw ApiException(message: 'فشل في تحديث التقييم');
    }
  }

  // حذف تقييم
  static Future<void> deleteReview(String reviewId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      _savedReviews.removeWhere((review) => review.id == reviewId);
    } catch (e) {
      throw ApiException(message: 'فشل في حذف التقييم');
    }
  }

  // جلب جميع التقييمات (للإدارة)
  static Future<List<Review>> getAllReviews() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));

      // ترتيب حسب التاريخ (الأحدث أولاً)
      final allReviews = List<Review>.from(_savedReviews);
      allReviews.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return allReviews;
    } catch (e) {
      throw ApiException(message: 'فشل في جلب جميع التقييمات');
    }
  }
}

// استجابة المصادقة
class AuthResponse {
  final User user;
  final String token;

  AuthResponse({required this.user, required this.token});
}

// استثناء API
class ApiException implements Exception {
  final int? statusCode;
  final String message;

  ApiException({this.statusCode, required this.message});

  @override
  String toString() => message;
}
