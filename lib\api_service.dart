import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'models/product.dart';
import 'models/user_simple.dart';
import 'models/category.dart';
import 'app_properties.dart';

class ApiService {
  static const String baseUrl = AppConstants.baseUrl;
  static const Duration timeoutDuration = Duration(seconds: 30);

  // Headers الأساسية
  static Map<String, String> get _baseHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Accept-Language': 'ar',
  };

  // Headers مع التوكن
  static Future<Map<String, String>> get _authHeaders async {
    final headers = Map<String, String>.from(_baseHeaders);
    final token = await _getAuthToken();
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  // الحصول على التوكن من التخزين المحلي
  static Future<String?> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(AppConstants.userTokenKey);
  }

  // حفظ التوكن في التخزين المحلي
  static Future<void> _saveAuthToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.userTokenKey, token);
  }

  // إزالة التوكن من التخزين المحلي
  static Future<void> _removeAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.userTokenKey);
  }

  // معالجة الاستجابة
  static dynamic _handleResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      if (response.body.isEmpty) return null;
      return json.decode(response.body);
    } else {
      throw ApiException(
        statusCode: response.statusCode,
        message: _getErrorMessage(response),
      );
    }
  }

  // الحصول على رسالة الخطأ
  static String _getErrorMessage(http.Response response) {
    try {
      final data = json.decode(response.body);
      return data['message'] ?? 'حدث خطأ غير متوقع';
    } catch (e) {
      switch (response.statusCode) {
        case 400:
          return 'طلب غير صحيح';
        case 401:
          return 'غير مصرح لك بالوصول';
        case 403:
          return 'ممنوع الوصول';
        case 404:
          return 'المورد غير موجود';
        case 500:
          return 'خطأ في الخادم';
        default:
          return 'حدث خطأ غير متوقع';
      }
    }
  }

  // ==================== خدمات المصادقة ====================

  // تسجيل الدخول
  static Future<AuthResponse> login(String email, String password) async {
    try {
      final response = await http
          .post(
            Uri.parse('$baseUrl/auth/login'),
            headers: _baseHeaders,
            body: json.encode({'email': email, 'password': password}),
          )
          .timeout(timeoutDuration);

      final data = _handleResponse(response);
      await _saveAuthToken(data['token']);

      return AuthResponse(
        user: User.fromJson(data['user']),
        token: data['token'],
      );
    } catch (e) {
      throw _handleException(e);
    }
  }

  // التسجيل
  static Future<AuthResponse> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    try {
      final response = await http
          .post(
            Uri.parse('$baseUrl/auth/register'),
            headers: _baseHeaders,
            body: json.encode({
              'email': email,
              'password': password,
              'firstName': firstName,
              'lastName': lastName,
              'phone': phone,
            }),
          )
          .timeout(timeoutDuration);

      final data = _handleResponse(response);
      await _saveAuthToken(data['token']);

      return AuthResponse(
        user: User.fromJson(data['user']),
        token: data['token'],
      );
    } catch (e) {
      throw _handleException(e);
    }
  }

  // إرسال رمز التحقق
  static Future<void> sendOTP(String phone) async {
    try {
      await http
          .post(
            Uri.parse('$baseUrl/auth/send-otp'),
            headers: _baseHeaders,
            body: json.encode({'phone': phone}),
          )
          .timeout(timeoutDuration);
    } catch (e) {
      throw _handleException(e);
    }
  }

  // التحقق من رمز OTP
  static Future<void> verifyOTP(String phone, String otp) async {
    try {
      final response = await http
          .post(
            Uri.parse('$baseUrl/auth/verify-otp'),
            headers: await _authHeaders,
            body: json.encode({'phone': phone, 'otp': otp}),
          )
          .timeout(timeoutDuration);

      _handleResponse(response);
    } catch (e) {
      throw _handleException(e);
    }
  }

  // نسيان كلمة المرور
  static Future<void> forgotPassword(String email) async {
    try {
      await http
          .post(
            Uri.parse('$baseUrl/auth/forgot-password'),
            headers: _baseHeaders,
            body: json.encode({'email': email}),
          )
          .timeout(timeoutDuration);
    } catch (e) {
      throw _handleException(e);
    }
  }

  // تسجيل الخروج
  static Future<void> logout() async {
    try {
      await http
          .post(Uri.parse('$baseUrl/auth/logout'), headers: await _authHeaders)
          .timeout(timeoutDuration);
    } catch (e) {
      // تجاهل الأخطاء في تسجيل الخروج
    } finally {
      await _removeAuthToken();
    }
  }

  // ==================== خدمات المنتجات ====================

  // جلب المنتجات
  static Future<List<Product>> getProducts({
    int page = 1,
    int limit = 20,
    String? categoryId,
    String? search,
    String? sortBy,
    double? minPrice,
    double? maxPrice,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (categoryId != null) queryParams['categoryId'] = categoryId;
      if (search != null) queryParams['search'] = search;
      if (sortBy != null) queryParams['sortBy'] = sortBy;
      if (minPrice != null) queryParams['minPrice'] = minPrice.toString();
      if (maxPrice != null) queryParams['maxPrice'] = maxPrice.toString();

      final uri = Uri.parse(
        '$baseUrl/products',
      ).replace(queryParameters: queryParams);
      final response = await http
          .get(uri, headers: _baseHeaders)
          .timeout(timeoutDuration);

      final data = _handleResponse(response);
      return (data['products'] as List)
          .map((json) => Product.fromJson(json))
          .toList();
    } catch (e) {
      throw _handleException(e);
    }
  }

  // جلب منتج بالمعرف
  static Future<Product> getProductById(String id) async {
    try {
      final response = await http
          .get(Uri.parse('$baseUrl/products/$id'), headers: _baseHeaders)
          .timeout(timeoutDuration);

      final data = _handleResponse(response);
      return Product.fromJson(data);
    } catch (e) {
      throw _handleException(e);
    }
  }

  // جلب المنتجات المميزة
  static Future<List<Product>> getFeaturedProducts() async {
    try {
      final response = await http
          .get(Uri.parse('$baseUrl/products/featured'), headers: _baseHeaders)
          .timeout(timeoutDuration);

      final data = _handleResponse(response);
      return (data as List).map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      throw _handleException(e);
    }
  }

  // ==================== خدمات الفئات ====================

  // جلب الفئات
  static Future<List<Category>> getCategories() async {
    try {
      final response = await http
          .get(Uri.parse('$baseUrl/categories'), headers: _baseHeaders)
          .timeout(timeoutDuration);

      final data = _handleResponse(response);
      return (data as List).map((json) => Category.fromJson(json)).toList();
    } catch (e) {
      // في حالة عدم توفر API، استخدم البيانات المحددة مسبقاً
      return PredefinedCategories.getCategories();
    }
  }

  // ==================== خدمات المستخدم ====================

  // جلب بيانات المستخدم الحالي
  static Future<User> getCurrentUser() async {
    try {
      final response = await http
          .get(Uri.parse('$baseUrl/user/profile'), headers: await _authHeaders)
          .timeout(timeoutDuration);

      final data = _handleResponse(response);
      return User.fromJson(data);
    } catch (e) {
      throw _handleException(e);
    }
  }

  // تحديث بيانات المستخدم
  static Future<User> updateUser(Map<String, dynamic> userData) async {
    try {
      final response = await http
          .put(
            Uri.parse('$baseUrl/user/profile'),
            headers: await _authHeaders,
            body: json.encode(userData),
          )
          .timeout(timeoutDuration);

      final data = _handleResponse(response);
      return User.fromJson(data);
    } catch (e) {
      throw _handleException(e);
    }
  }

  // معالجة الاستثناءات
  static Exception _handleException(dynamic e) {
    if (e is SocketException) {
      return ApiException(message: 'لا يوجد اتصال بالإنترنت');
    } else if (e is http.ClientException) {
      return ApiException(message: 'خطأ في الاتصال');
    } else if (e is ApiException) {
      return e;
    } else {
      return ApiException(message: 'حدث خطأ غير متوقع');
    }
  }
}

// استجابة المصادقة
class AuthResponse {
  final User user;
  final String token;

  AuthResponse({required this.user, required this.token});
}

// استثناء API
class ApiException implements Exception {
  final int? statusCode;
  final String message;

  ApiException({this.statusCode, required this.message});

  @override
  String toString() => message;
}
