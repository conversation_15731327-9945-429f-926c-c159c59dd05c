@echo off
title VisionLens App Terminal
cd /d "G:\visionlensapp\visionlens_app"
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    VisionLens App Terminal                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📍 المسار الحالي: %CD%
echo.
echo 🚀 الأوامر السريعة:
echo    1. flutter run -d chrome --web-port 8080    (تشغيل على كروم)
echo    2. flutter build web                        (بناء المشروع)
echo    3. firebase deploy                          (نشر على Firebase)
echo    4. flutter clean                            (تنظيف المشروع)
echo    5. flutter doctor                           (فحص Flutter)
echo.
echo ═══════════════════════════════════════════════════════════════
echo.
cmd /k
