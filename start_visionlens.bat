@echo off
title VisionLens App - تشغيل التطبيق
cd /d "G:\visionlensapp"
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    VisionLens App                           ║
echo ║                  تم إعادة تنظيم المجلدات                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📍 المسار الحالي: %CD%
echo 📁 بنية المشروع الجديدة:
echo    ├── lib\                 (ملفات التطبيق الرئيسية)
echo    ├── firebase.json        (إعدادات Firebase)
echo    ├── firestore.rules      (قواعد قاعدة البيانات)
echo    └── pubspec.yaml         (إعدادات المشروع)
echo.
echo 🚀 الأوامر السريعة:
echo    1. flutter run -d chrome --web-port 8080    (تشغيل على كروم)
echo    2. flutter build web                        (بناء المشروع)
echo    3. firebase deploy                          (نشر على Firebase)
echo    4. flutter clean                            (تنظيف المشروع)
echo    5. flutter doctor                           (فحص Flutter)
echo.
echo ═══════════════════════════════════════════════════════════════
echo.
echo 🎯 تشغيل التطبيق على كروم...
flutter run -d chrome --web-port 8080
